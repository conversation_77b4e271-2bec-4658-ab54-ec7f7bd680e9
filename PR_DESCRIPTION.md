# GY-1450: Assign User to Group Functionality

## Overview
This PR implements the user group assignment functionality for the auth-mservice, allowing users to be automatically assigned to multiple groups during the user creation process. The implementation provides a flexible approach using value objects and supports both new enhanced APIs and backward compatibility with legacy group assignment methods.

## Changes

### Core Functionality
- **Enhanced User Group Assignment**: Implemented comprehensive user-to-group assignment functionality during user creation
- **Flexible Group References**: Added support for multiple group reference types (ID, NAME, EXTERNAL_ID, CODE)
- **Auto-Creation Support**: Groups can be automatically created if they don't exist when referenced by name
- **Batch Group Assignment**: Users can be assigned to multiple groups in a single operation
- **Robust Error Handling**: Partial failure handling ensures user creation succeeds even if some group assignments fail

### New Classes and Components
- **`CreateUserRequest.java`**: New enhanced request class extending `AccountRequest` with group assignment capabilities
- **`GroupReference.java`**: Value object for flexible group identification with multiple reference types
- **`GroupReferenceType.java`**: Enum defining different group reference types (ID, NAME, EXTERNAL_ID, CODE)
- **`GroupType.java`**: Enum for group categories (Department, Project Team, etc.)

### Service Enhancements
- **UserService**:
  - Added `assignUserToGroups()` method for multi-group assignment
  - Added `processGroupReference()` method for handling different reference types
  - Enhanced user creation workflow to support group assignment
  - Maintained backward compatibility with legacy `assignUserToGroup()` method
- **UserGroupService**:
  - Added `findOrCreateGroupByName()` method for automatic group creation
  - Added group lookup methods: `getById()`, `findGroupByName()`, `findGroupByExternalId()`, `findGroupByCode()`
  - Enhanced group management capabilities

### API Enhancements
- **CompanyResource**: Updated user creation endpoints to support the new `CreateUserRequest` with group assignment
- Enhanced request validation and error handling for group assignment scenarios

### Configuration and Dependencies
- **Dependency Updates**: Updated base-service version and other dependencies
- **Deployment Configurations**: Updated deployment configurations across multiple environments (AWS QA/Staging, GCP Staging/UAT)
- **CI/CD Pipeline**: Updated GitLab CI configuration for improved deployment process

## Key Features

### Multiple Group Assignment Approaches
1. **Enhanced API (Recommended)**: Use `CreateUserRequest` with `Set<GroupReference>` for maximum flexibility
2. **Legacy Support**: Backward compatible with existing `groupName` property in request

### Group Reference Types
- **By ID**: Assign to existing groups using their database ID
- **By Name**: Assign by group name with optional auto-creation
- **By External ID**: Assign using external system identifiers
- **By Code**: Assign using group codes

### Error Handling
- **Graceful Degradation**: User creation succeeds even if some group assignments fail
- **Comprehensive Logging**: Detailed logging for successful and failed group assignments
- **Validation**: Input validation for group references and user data

## Testing Scenarios
The implementation supports the following scenarios:
- Single group assignment by name (with auto-creation)
- Multiple group assignment using different reference types
- Mixed reference types in a single request
- Partial failure handling (some groups succeed, others fail)
- Legacy group assignment using `groupName` property
- No group assignment (standard user creation)

## Backward Compatibility
- Existing APIs continue to work without modification
- Legacy `groupName` property in request properties is still supported
- No breaking changes to existing user creation workflows

## Security Considerations
- All group assignments follow existing security protocols
- User permissions are validated before group assignment
- Group creation permissions are enforced when auto-creating groups
- Audit logging for all group assignment operations

## Deployment Instructions
1. Deploy the updated service to the target environment
2. The new functionality is backward compatible and requires no configuration changes
3. Optional: Configure group auto-creation policies if needed
4. Verify group assignment functionality in test environment before production deployment

## Related Issues
- [GY-1450](https://jira.glideyoke.com/browse/GY-1450) - Assign user to Group functionality
